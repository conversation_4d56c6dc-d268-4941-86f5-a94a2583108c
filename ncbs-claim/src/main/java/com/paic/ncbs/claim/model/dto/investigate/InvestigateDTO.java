package com.paic.ncbs.claim.model.dto.investigate;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ApiModel("调查信息")
public class InvestigateDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("调查信息表主键")
    private String idAhcsInvestigate;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("事故者现状：医疗、伤残（取自数据字典）")
    private String insuredApplyStatus;

    @ApiModelProperty(" 事故场景编号，取自数据字典。如有多个，用英文逗号分隔")
    private String accidentScene;

    @ApiModelProperty(" 事故场景编号name")
    private String accidentSceneName;

    @ApiModelProperty("提调环节")
    private String initiateInvestigateNode;

    @ApiModelProperty("提调环节的任务实例id")
    private String taskInstId;

    @ApiModelProperty("发起人")
    private String initiatorUm;

    @ApiModelProperty("调查状态")
    private Integer investigateStatus =1 ;

    @ApiModelProperty("调查状态-打印中心-公估委托书展示用")
    private String processStatus;

    @ApiModelProperty("提调事项")
    private String investigateItems;

    @ApiModelProperty("调查机构")
    private String investigateDepartment;

    @ApiModelProperty("主调人员")
    private String primaryInvestigatorUm;

    @ApiModelProperty("提调类型 (01内部调查02外部调查)")
    private String initMode;

    @ApiModelProperty("调查结论")
    private String investigateConclusion;

    @ApiModelProperty("分配信息：分配人|分配意见|分配时间|已审核人code")
    private String allocateInfo;

    @ApiModelProperty(" 调查定性（取数据字典 正常件、参考件、异常件）")
    private String investigateQualitative;

    @ApiModelProperty("匹配到的风控规则组id")
    private String idRmRunBatchRecord;

    @ApiModelProperty("是否为评定追加的调查，“N”或者为空就认为不是")
    private String isEvaluateInvestigate;

    @ApiModelProperty("是否是旧的调查")
    private String isOldInvestigate;

    @ApiModelProperty("是否包含公估费")
    private String isHasAdjustingFee;

    @ApiModelProperty("公估费审核是否通过(Y/N)；当主调选择包含公估费的时候此字段有值")
    private String feeAuditOption;

    @ApiModelProperty("提调机构")
    private String initiateDepartment;

    @ApiModelProperty("预估费用")
    private BigDecimal estimate;

    @ApiModelProperty("其他")
    private String other;

    @ApiModelProperty("操作状态 0：暂存 1提交")
    private Integer operate;

    @ApiModelProperty(value = "公估费")
    private BigDecimal commonEstimateFee  ;

    private String taskCode;
    /**
     * 理赔处理方式1：TPA全流程，2:TPA半流程
     */
    @ApiModelProperty("理赔处理方式1：TPA全流程，2:TPA半流程")
    private String claimDealWay;

    @ApiModelProperty("外部调查标记，N-旧流程 Y-新流程")
    private String investigateFlag;

    @ApiModelProperty("公估公司服务代码")
    private String serverCode;

    @ApiModelProperty("提调审批时间-任务完成时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date completeTime;

    @ApiModelProperty("审核人")
    private String auditorUm;

    @ApiModelProperty("审核人机构号")
    private String auditorDepartmentCode;

    @ApiModelProperty("审核人名称")
    private String auditorName;
}